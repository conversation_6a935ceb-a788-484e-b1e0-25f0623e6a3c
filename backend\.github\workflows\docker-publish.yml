name: Build and Push to Docker Hub # 工作流的名称，会显示在 GitHub Actions 页面

on: # 定义触发工作流的事件
  push:
    branches: [ master ] # 当代码被推送到 main 分支时触发 (你可以改成你的主分支名，如 master)
  # pull_request: # 你也可以在 Pull Request 时触发构建，但不一定推送
  #   branches: [ main ]

jobs: # 工作流包含一个或多个 job，这里只有一个名为 build 的 job
  build:
    runs-on: ubuntu-latest # 指定运行这个 job 的虚拟环境类型

    steps: # job 包含一系列步骤
    - name: Checkout repository # 步骤1: 检出你的代码
      uses: actions/checkout@v4 # 使用官方的 checkout action

    - name: Set up Docker Buildx # 步骤2: 设置 Buildx，这是 Docker CLI 插件，用于扩展构建能力
      uses: docker/setup-buildx-action@v3

    - name: Login to Docker Hub # 步骤3: 登录到 Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }} # 从 GitHub Secrets 读取 Docker Hub 用户名
        password: ${{ secrets.DOCKERHUB_TOKEN }}    # 从 GitHub Secrets 读取 Docker Hub 访问令牌 (推荐)

    - name: Build and push Docker image # 步骤4: 构建并推送 Docker 镜像
      uses: docker/build-push-action@v5
      with:
        context: . # Dockerfile 的路径，. 表示在仓库根目录
        file: ./Dockerfile # 明确指定 Dockerfile 的路径 (如果不在根目录或有特殊名称)
        push: true # 设置为 true 表示推送到仓库
        tags: |
            ${{ secrets.DOCKERHUB_USERNAME }}/app1_backend:${{ github.sha }}
            ${{ secrets.DOCKERHUB_USERNAME }}/app1_backend:latest