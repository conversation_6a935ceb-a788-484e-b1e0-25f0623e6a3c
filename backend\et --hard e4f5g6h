[33m7df6bd4[m[33m ([m[1;36mHEAD[m[33m -> [m[1;32mmain[m[33m, [m[1;31morigin/main[m[33m, [m[1;31morigin/HEAD[m[33m)[m fuck openai !
[33mccfe582[m fuck openai !
[33m9451ff6[m fuck openai !
[33m3487727[m fuck openai !
[33m115e96f[m[33m ([m[1;31mgithub/master[m[33m, [m[1;31mgithub/main[m[33m)[m ..
[33m3fcae05[m oai wcmn!
[33mbceb785[m oai wcmn!
[33m642b791[m nb prompt
[33ma85fae9[m nb prompt
[33m4fc7688[m nb prompt
[33m74b4fbf[m fix bug
[33mcb16a08[m fix bug
[33m6a1c7c5[m fix bug
[33m51a091d[m fix bug
[33m39f5372[m fix bug
[33m445f199[m fix bug
[33m4cedd9a[m fix bug
[33m95bade0[m fix bug
[33m56952c4[m fix bug
[33m73d1676[m fix bug
[33m01dfb13[m fix bug
[33m5795771[m fix bug
[33m8ddc386[m fix bug
[33m6970c19[m fix bug
[33m3a5d1d5[m fix bug
[33m412b66e[m fix bug
[33mc5f5ab8[m fix bug
[33mcbfa21a[m Update Dockerfile
[33m0583e6d[m Update run.py
[33mfa8e70b[m Update run.py
[33m9b02ab1[m Update Dockerfile
[33m338bb7b[m Update Dockerfile
[33m477ef7e[m Update Dockerfile
[33m2c0a798[m Update requirements.txt
[33m54d1645[m Update requirements.txt
[33mf288057[m 00
[33m80e8115[m 00
[33m94f98c7[m 00
[33m4ba2a61[m 00
[33mf286238[m feat: Configure project for Hugging Face Space deployment
[33m112c4c4[m 00.1
[33mf16592d[m 00.1
[33m3d6b169[m 00.1
[33m5c04c72[m 重构整个代码 增强可读性 修复已知代码逻辑缺陷
[33md47764d[m 重构整个代码 增强可读性 修复已知代码逻辑缺陷
[33mcce4ac8[m 重构整个代码 增强可读性 修复已知代码逻辑缺陷
[33me269f8e[m 重构整个代码 增强可读性 修复已知代码逻辑缺陷
[33mb655350[m 1
[33m4141250[m 0
[33m7435763[m 1
[33m310563c[m 1
[33m136b736[m 616
[33me0842d8[m 616
[33mf0aa8f2[m 2025/6/15 01:16
[33m665962e[m 2025-6-11 18:10
[33mc09ba12[m 2025-6-11 18:01
[33mc084215[m 2025-6-11 17:53
[33m78bae05[m 2025-6-11 17:44
[33m25138f6[m 2025-6-11
[33mc753f71[m 2025-6-6 22:29
[33m143040a[m 2025-6-6 21:41
[33m399c06c[m 2025-6-6 21:41
[33m7678a3b[m 2025-6-6 21:41
[33m9206e2d[m 2025-6-6 21:41
[33m69f75af[m 2025-6-6 21:39
[33m7f8f2d2[m 2025-6-6 21:32
[33mc02a07c[m 2025-6-6 21:24
[33me77efed[m 2025-6-6 20:17
[33md35c8ce[m 2025-6-6 20:17
[33ma0d389b[m 2025-6-6 20:17
[33m4303ced[m 2025-6-6 20:17
[33mf6572e5[m 2025-6-6 20:17
[33mf5be593[m 2025-6-6 18:49
[33m75251b9[m 2025-6-6 18:39
[33m4becd65[m 2025-6-6 18:33
[33mb6e121f[m 2025-6-6 18:17
[33mf57967c[m 2025-6-6 18:12
[33m6185a25[m 2025-6-6 17:58
[33m5ae9326[m 2025-6-6 17:53
[33m9cb958c[m 2025-6-6 17:15
[33m0c4a04f[m 2025-6-6 17:04
[33m7be260b[m 2025-6-6-16:28
[33mbb5e01c[m 0605-1954
[33m710691d[m 0605-1950
[33m5dc6631[m 0605-1944
[33m4f5e6d1[m 0605-1942
[33m1c77507[m 0605-test
[33mf5d6330[m 0605-1913
[33mcd84bc8[m 0605-1907
[33m1f63e71[m 0605-1845
[33m525358d[m hf
[33m9c0e994[m hf
[33mbe5fa7f[m hf
[33m8e075f6[m hf
[33me3a2cd6[m Revert "？"
[33m21e4c10[m Revert "？"
[33m1e7441e[m Revert "..."
[33m8a2d154[m Revert "..."
[33m026771f[m ??
[33m42a84f6[m v v
[33m013cb3d[m v v
[33mfbf1731[m ...
[33m7a180fb[m ...
[33m78b30c1[m ？
[33m5ca9fb6[m ？
[33m1892537[m ？
[33m7c4c360[m ？
[33ma86d001[m ？
[33mf5f704e[m ？
[33m6a0504b[m ？
[33md5d9e5e[m 50MB
[33ma559312[m 50MB
[33mfa3c7b5[m 支持文档上传
[33mf1769e7[m 添加文档识别功能。。。
[33mdb2f059[m 添加文档识别功能。。。
[33m36a9a3a[m 添加文档识别功能。。。
[33m7e0873d[m 添加文档识别功能。。。
[33m745f415[m 添加文档识别功能。。。
[33m3b915ef[m 添加文档识别功能。。。
[33m6894d36[m 添加文档识别功能。。。
[33m2fea60d[m 添加文档识别功能。。。
[33m6d7df4c[m 添加文档识别功能。。。
[33m2a63e89[m 添加文档识别功能。。。
[33mc3b4335[m 修复已知bug
[33m2bd7489[m 实现图像识别功能
[33ma1ee5b7[m 实现图像分析功能(仅限Google-Gemini系)
[33m03f0a80[m 实现图像分析功能(仅限Google-Gemini系)
[33m38449ef[m 666
[33m6ec2361[m  ??
[33mb30aea4[m  1
[33m8150a9d[m  1
[33m17ac6bf[m  1
[33mfbb02e9[m 优化propmt
[33m445541a[m 优化propmt
[33m5c158e2[m 优化propmt
[33m5605dc9[m 优化propmt
[33m5ca2f36[m 优化propmt
[33m9f35042[m 优化propmt
[33m01f4ae7[m 唉...
[33m7d697d5[m push vercel
[33mdf26c87[m docker hub push
[33m65f2db5[m docker hub2
[33mefc1dde[m docker hub2
[33m9eeba3c[m docker hub
[33m780a7f9[m 嗯嗯嗯
[33md1ebb4b[m Add missing router include in main.py
[33m11d1fb9[m 。。。
[33ma3b8a36[m Fix README.md colorFrom and colorTo for Hugging Face Spaces
[33m2e4c5d4[m Fix README.md python_version for Docker SDK
[33m720aa9c[m Add Hugging Face Space configuration to README.md
[33me10a2e9[m Add Hugging Face Space configuration to README.md
[33m53d67a8[m Add Hugging Face Space configuration to README.md
[33mff55af9[m Update Dockerfile
[33m7442638[m Update Dockerfile
[33mdfe81f2[m Add Hugging Face Space configuration to README.md
[33mb6162b5[m Add Hugging Face Space configuration to README.md
[33m6cba653[m Add Hugging Face Space configuration to README.md
[33mdd33971[m 555
[33mf375e4d[m 重构整个后端代码
