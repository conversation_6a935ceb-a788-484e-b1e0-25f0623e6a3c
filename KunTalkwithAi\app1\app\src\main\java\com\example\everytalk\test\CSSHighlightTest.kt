package com.example.everytalk.test

import android.util.Log
import androidx.compose.ui.text.AnnotatedString
import com.example.everytalk.util.CodeHighlighter

fun testCSSHighlighting() {
    val cssCode = """
        /* 这是CSS注释 */
        .container {
            display: flex;
            width: 100%;
            height: 50vh;
            background-color: #ff6600;
            margin: 10px auto;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        #main-header {
            color: rgb(255, 255, 255);
            font-size: 24px;
            font-weight: bold;
            text-align: center;
        }
        
        .button:hover {
            background: linear-gradient(45deg, #ff0000, #00ff00);
            transform: scale(1.05);
            transition: all 0.3s ease-in-out;
        }
        
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
                padding: 10px;
            }
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
        }
    """.trimIndent()
    
    Log.d("CSSTest", "测试CSS代码:")
    Log.d("CSSTest", cssCode)
    Log.d("CSSTest", "开始高亮处理...")

    val result = CodeHighlighter.highlightToAnnotatedString(cssCode, "css")

    Log.d("CSSTest", "高亮结果:")
    Log.d("CSSTest", "原始文本长度: ${cssCode.length}")
    Log.d("CSSTest", "结果文本长度: ${result.text.length}")
    Log.d("CSSTest", "样式数量: ${result.spanStyles.size}")

    if (result.spanStyles.isEmpty()) {
        Log.e("CSSTest", "❌ 警告：没有应用任何样式！")
    } else {
        Log.i("CSSTest", "✅ 成功应用了 ${result.spanStyles.size} 个样式")

        // 显示前10个样式作为示例
        result.spanStyles.take(10).forEachIndexed { index, spanStyle ->
            val start = spanStyle.start
            val end = spanStyle.end
            val text = result.text.substring(start, end)
            val color = spanStyle.item.color
            Log.d("CSSTest", "样式 ${index + 1}: '$text' -> 颜色: $color (位置: $start-$end)")
        }

        if (result.spanStyles.size > 10) {
            Log.d("CSSTest", "... 还有 ${result.spanStyles.size - 10} 个样式")
        }
    }
}

fun main() {
    testCSSHighlighting()
}
